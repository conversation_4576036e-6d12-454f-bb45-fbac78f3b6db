
const {
  CognitoIdentityProviderClient,
  AdminSetUserPasswordCommand,
  AdminGetUserCommand,
  AdminConfirmSignUpCommand
} = require('@aws-sdk/client-cognito-identity-provider');
const client = new CognitoIdentityProviderClient({ region: process.env.AWS_REGION || 'us-east-1' });

exports.handler = async (event) => {

  const userPoolId = process.env.AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLID;
  let username, password, verificationCode;

  // Support API Gateway event structure
  if (event.body) {
    try {
      const body = JSON.parse(event.body);
      username = body.username || body.email;
      password = body.password;
      verificationCode = body.verificationCode || body.code;
    } catch (e) {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({ error: 'Invalid request body' })
      };
    }
  } else {
    username = event.username || event.email;
    password = event.password;
    verificationCode = event.verificationCode || event.code;
  }

  if (!username || !password || !verificationCode) {
    return {
      statusCode: 400,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({ error: 'username, password, and verificationCode are required' })
    };
  }

  try {
    // First, confirm the signup with the verification code
    const confirmCmd = new AdminConfirmSignUpCommand({
      UserPoolId: userPoolId,
      Username: username,
    });
    await client.send(confirmCmd);

    // Set permanent password
    const setPwdCmd = new AdminSetUserPasswordCommand({
      UserPoolId: userPoolId,
      Username: username,
      Password: password,
      Permanent: true
    });
    await client.send(setPwdCmd);

    // Verify user status
    const getUserCmd = new AdminGetUserCommand({
      UserPoolId: userPoolId,
      Username: username
    });
    const result = await client.send(getUserCmd);

    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({
        message: 'User verified and confirmed successfully',
        userStatus: result.UserStatus,
        email: username
      })
    };
  } catch (err) {
    console.error("Error confirming user:", err);

    // Handle specific errors
    let errorMessage = 'Verification failed';
    let statusCode = 500;

    if (err.name === 'CodeMismatchException') {
      errorMessage = 'Invalid verification code';
      statusCode = 400;
    } else if (err.name === 'ExpiredCodeException') {
      errorMessage = 'Verification code has expired';
      statusCode = 400;
    } else if (err.name === 'UserNotFoundException') {
      errorMessage = 'User not found';
      statusCode = 404;
    }

    return {
      statusCode: statusCode,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({ error: errorMessage, details: err.message })
    };
  }
};
