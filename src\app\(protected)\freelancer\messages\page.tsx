"use client";

import { useAuth } from "@/lib/auth/AuthContext";

export default function FreelancerMessagesPage() {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="h-full flex items-center justify-center">
        <p>Loading user data...</p>
      </div>
    );
  }

  return (
    <div className="h-full flex-1 flex items-center justify-center bg-muted/50">
      <div className="text-center p-6 max-w-md">
        <h2 className="text-2xl font-bold mb-2">Your Messages</h2>
        <p className="text-muted-foreground mb-6">
          Select a conversation or start a new one to begin messaging.
        </p>
      </div>
    </div>
  );
}
