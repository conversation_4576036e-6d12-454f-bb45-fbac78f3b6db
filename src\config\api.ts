// API Configuration
export const API_CONFIG = {
  // Base URL for API requests - ensure it includes the stage
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://qj8altpvmi.execute-api.us-east-1.amazonaws.com/local',

  // Authentication endpoints
  AUTH: {
    SIGNUP: '/auth/signup',
    // Add other auth endpoints here as needed
  },

  // Helper function to get full API URL
  getUrl: (endpoint: string): string => {
    // Remove any leading/trailing slashes for consistency
    const base = API_CONFIG.BASE_URL.replace(/\/+$/, '');
    const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const fullUrl = `${base}${path}`;

    // Debug logging in development
    if (process.env.NODE_ENV === 'development') {
      console.log('API URL:', fullUrl);
    }

    return fullUrl;
  }
};
