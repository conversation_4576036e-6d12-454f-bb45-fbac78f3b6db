"use client";
import React, { createContext, useContext, useEffect, useState } from "react";
import {
  signIn as authSignIn,
  confirmSignUp as authConfirmSignUp,
  getCurrentUser as authGetCurrentUser,
  resetPassword as authResetPassword,
  confirmResetPassword,
  fetchUserAttributes,
  type SignInOutput,
  type AuthError,
} from "aws-amplify/auth";
import { Hub } from "@aws-amplify/core";
import { authService } from "./AuthService";
import * as AuthTypes from "@/types/auth";

// Import enums directly
import { UserRole } from "@/types/enums";
type CognitoUser = AuthTypes.CognitoUser;
type SignUpParams = AuthTypes.SignUpParams;
type ProfileUpdateParams = AuthTypes.ProfileUpdateParams;
type ChallengeUser = AuthTypes.ChallengeUser;
type CustomSignInOutput = AuthTypes.CustomSignInOutput;
const { createCognitoUser } = AuthTypes;

interface AuthContextType extends AuthTypes.AuthContextType {
  isAuthenticated: boolean;
  user: CognitoUser | null;
  loading: boolean;
  error: string | null;
  cognitoUserId: string | null;
  signIn: (email: string, password: string) => Promise<CognitoUser | void>;
  signUp: (params: SignUpParams) => Promise<CognitoUser>;
  confirmSignUp: (
    username: string,
    code: string
  ) => Promise<SignInOutput | boolean>;
  signOut: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  forgotPasswordSubmit: (
    email: string,
    code: string,
    newPassword: string
  ) => Promise<void>;
  completeNewPassword: (
    user: ChallengeUser,
    newPassword: string
  ) => Promise<SignInOutput>;
  updateProfile: (attributes: ProfileUpdateParams) => Promise<void>;
  refresh: () => Promise<CognitoUser>;
};

/**
 * The authentication context that will be used to provide auth state and methods
 * to components in the application.
 */
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * AuthProvider component that wraps the application and provides authentication context.
 *
 * @param children - Child components that will have access to the auth context
 * @returns A React component that provides authentication context to its children
 */
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<CognitoUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Effect hook to check authentication state on mount and set up auth event listeners.
   * This runs once when the component mounts and cleans up when it unmounts.
   */
  useEffect(() => {
    // Check auth state on mount
    checkAuthState();

    interface HubPayloadData {
      username?: string;
      attributes?: {
        email?: string;
        name?: string;
        bio?: string;
        skills?: string[];
        'custom:role'?: UserRole;
        [key: string]: unknown;
      };
      [key: string]: unknown;
    }

    interface HubPayload {
      event: string;
      data?: HubPayloadData;
    }

    const hubListener = async (data: { payload: HubPayload }) => {
      switch (data.payload.event) {
        case "signIn":
        case "autoSignIn":
          try {
            const payloadData = data.payload.data;
            if (payloadData?.username) {
              const userData: CustomSignInOutput = {
                ...payloadData,
                username: payloadData.username || "",
                attributes: {
                  email: payloadData.attributes?.email || "",
                  name: payloadData.attributes?.name || "",
                  bio: payloadData.attributes?.bio || "",
                  skills: payloadData.attributes?.skills || [],
                  "custom:role":
                    (payloadData.attributes?.["custom:role"] as UserRole) || "CLIENT" as UserRole,
                },
                isSignedIn: true,
                nextStep: { signInStep: "DONE" },
              };
              setUser(userData);
            } else {
              const currentUser = await authGetCurrentUser();
              const attributes = await fetchUserAttributes();
              const userData: CustomSignInOutput = {
                ...currentUser,
                username: currentUser.username || "",
                attributes: {
                  email: attributes.email || "",
                  name: attributes.name || "",
                  bio: attributes.bio || "",
                  skills: Array.isArray(attributes.skills) ? attributes.skills : [],
                  "custom:role":
                    (attributes["custom:role"] as UserRole) || "CLIENT" as UserRole,
                },
                name: attributes.name || "",
                bio: attributes.bio || "",
                skills: Array.isArray(attributes.skills) ? attributes.skills : [],
                isSignedIn: true,
                nextStep: { signInStep: "DONE" },
              };
              setUser(userData);
            }
            setIsAuthenticated(true);
          } catch (error) {
            console.error("Error handling auth event:", error);
            setUser(null);
            setIsAuthenticated(false);
          }
          setIsAuthenticated(true);
          setError(null);
          break;

        case "signOut":
          setUser(null);
          setIsAuthenticated(false);
          setError(null);
          break;

        case "signIn_failure":
          setError(
            (data.payload.data?.message as string) || "Failed to sign in"
          );
          setIsAuthenticated(false);
          setUser(null);
          break;

        case "tokenRefresh_failure":
          setError("Your session has expired. Please sign in again.");
          setIsAuthenticated(false);
          setUser(null);
          break;
      }
    };

    const hubListenerCleanup = Hub.listen(
      "auth",
      hubListener as Parameters<typeof Hub.listen>[1]
    );

    return () => {
      if (hubListenerCleanup) {
        hubListenerCleanup();
      }
    };
  }, []);

  /**
   * Checks the current authentication state by attempting to get the current user.
   * Updates the auth state in the context based on whether a user is authenticated.
   */
  const checkAuthState = async () => {
    try {
      setLoading(true);
      const currentUser = await authGetCurrentUser();
      if (currentUser) {
        const attributes = await fetchUserAttributes();
        const userData: CustomSignInOutput = {
          ...currentUser,
          username: currentUser.username || "",
          attributes: {
            email: attributes.email || "",
            name: attributes.name || "",
            "custom:role": attributes["custom:role"] as UserRole | undefined,
          },
          isSignedIn: true,
          nextStep: { signInStep: "DONE" },
        };
        setUser(userData);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error("Error checking auth state:", error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Signs in a user with the provided email and password.
   *
   * @param email - The user's email address
   * @param password - The user's password
   * @returns A Promise that resolves with the authenticated user or void if an error occurs
   * @throws {Error} If authentication fails
   */
  const signIn = async (
    email: string,
    password: string
  ): Promise<CognitoUser | void> => {
    try {
      setLoading(true);
      setError(null);
      const authUser = await authService.signIn(email, password);

      // Handle the case where a new password is required
      if (authUser && typeof authUser === "object" && "nextStep" in authUser) {
        if (
          authUser.nextStep.signInStep ===
          "CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED"
        ) {
          // Return a CognitoUser object that can be used to handle the new password requirement
          // Create a new user object without isSignedIn and nextStep
          const newUser = {
            username: email,
            attributes: {
              email: email,
              "custom:role": "CLIENT" as UserRole,
            },
            challengeName: "NEW_PASSWORD_REQUIRED",
          };

          // Then use createCognitoUser to add the required properties
          return createCognitoUser(newUser);
        }
      }

      // If we get here, sign in was successful
      const currentUser = await authService.getCurrentUser();

      // Handle the case where currentUser is null
      if (!currentUser) {
        throw new Error("Failed to get current user after sign in");
      }

      // Get the user's attributes
      const userAttributes = await fetchUserAttributes();
      const roleValue = userAttributes["custom:role"] as UserRole | undefined;
      const userRole: UserRole = roleValue && Object.values(UserRole).includes(roleValue as UserRole)
        ? roleValue as UserRole
        : UserRole.CLIENT;

      const cognitoUser = createCognitoUser({
        username: currentUser.username || email,
        attributes: {
          email: currentUser.signInDetails?.loginId || email,
          name:
            userAttributes.name ||
            userAttributes.given_name ||
            userAttributes.nickname ||
            "",
          "custom:role": userRole,
        },
      });

      // Update the user in the context
      setUser(cognitoUser);
      setIsAuthenticated(true);

      return cognitoUser;
    } catch (error) {
      const authError = error as AuthError;
      const errorMessage = authError?.message || "Failed to sign in";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const [signupData, setSignupData] = useState<{
    email: string;
    password: string;
  } | null>(null);

  /**
   * Signs up a new user with the provided parameters.
   *
   * @param params - Object containing signup parameters (username, password, attributes)
   * @returns A Promise that resolves with the newly created user
   * @throws {Error} If signup fails
   */
  const signUp = async (params: SignUpParams) => {
    try {
      setLoading(true);
      setError(null);

      const userRole: UserRole = (params.attributes["custom:role"] as UserRole) || UserRole.CLIENT;

      setSignupData({
        email: params.username,
        password: params.password,
      });

      await authService.signUp(
        {
          email: params.username,
          password: params.password,
          name: params.attributes.name || "",
          role: userRole,
        },
        userRole
      );

      const cognitoUser = createCognitoUser({
        username: params.username,
        attributes: {
          email: params.attributes.email,
          name: params.attributes.name || "",
          "custom:role": userRole,
        },
      });

      return cognitoUser;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to sign up";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Confirms a user's signup with the provided confirmation code.
   *
   * @param username - The username to confirm
   * @param code - The confirmation code received by the user
   * @returns A Promise that resolves with the sign-in result or true if auto-signin is not possible
   * @throws {Error} If confirmation fails
   */
  const confirmSignUp = async (
    username: string,
    code: string
  ): Promise<SignInOutput | boolean> => {
    try {
      setLoading(true);
      setError(null);

      await authConfirmSignUp({ username, confirmationCode: code });

      if (signupData) {
        const { email, password } = signupData;
        if (email && password) {
          const result = await signIn(email, password);
          if (result) {
            return result;
          }
          return true;
        }
      }

      return true;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to confirm sign up";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Updates the current user's profile with the provided attributes.
   *
   * @param attributes - The attributes to update
   * @returns A Promise that resolves when the update is complete
   * @throws {Error} If the update fails
   */
  const updateProfile = async (attributes: ProfileUpdateParams) => {
    try {
      setLoading(true);
      setError(null);

      // In a real implementation, you would update the user's attributes in Cognito
      // For now, we'll just update the local state
      if (user) {
        const updatedUser = {
          ...user,
          attributes: {
            ...user.attributes,
            ...attributes,
          },
        };
        setUser(updatedUser);
      }

      // If you're using a backend API to store additional profile data, you would call it here
      // await api.updateUserProfile(attributes);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update profile";
      console.error("Error updating profile:", error);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh the current user's data
  const refresh = async () => {
    try {
      const currentUser = await authGetCurrentUser();
      const attributes = await fetchUserAttributes();
      const userData: CustomSignInOutput = {
        ...currentUser,
        username: currentUser.username || "",
        attributes: {
          email: attributes.email || "",
          name: attributes.name || "",
          "custom:role": (attributes["custom:role"] as UserRole) || "CLIENT",
        },
        isSignedIn: true,
        nextStep: { signInStep: "DONE" },
      };
      setUser(userData);
      return userData;
    } catch (error) {
      console.error("Error refreshing user data:", error);
      setUser(null);
      setIsAuthenticated(false);
      throw error;
    }
  };

  // Complete the context value with all required methods
  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    loading,
    error,
    cognitoUserId: user?.username || null,
    signIn,
    signUp,
    confirmSignUp,
    updateProfile,
    signOut: async () => {
      try {
        setLoading(true);
        await authService.signOut();
        setUser(null);
        setIsAuthenticated(false);
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to sign out";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    forgotPassword: async (email: string) => {
      try {
        setLoading(true);
        await authResetPassword({ username: email });
        setError(null);
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to reset password";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    forgotPasswordSubmit: async (
      email: string,
      code: string,
      newPassword: string
    ) => {
      try {
        setLoading(true);
        await confirmResetPassword({
          username: email,
          confirmationCode: code,
          newPassword,
        });
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to reset password";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    refresh,
    completeNewPassword: async (challengeUser: ChallengeUser, newPassword: string) => {
      try {
        setLoading(true);
        
        const username = challengeUser.username;
        if (!username) {
          throw new Error('Username is required');
        }
        
        // Sign in with the new password
        const signInResult = await authSignIn({
          username,
          password: newPassword,
        });
        
        if (!signInResult.isSignedIn) {
          throw new Error('Failed to sign in with new password');
        }

        // Get the latest user attributes after successful sign-in
        const attributes = await fetchUserAttributes();
        
        // Create a complete user object with all required properties
        const userData: CustomSignInOutput = {
          ...signInResult,
          username,
          attributes: {
            email: attributes.email || '',
            name: attributes.name || '',
            bio: attributes.bio || '',
            skills: Array.isArray(attributes.skills) ? attributes.skills : [],
            "custom:role": (attributes["custom:role"] as UserRole) || "CLIENT" as UserRole,
          },
          name: attributes.name || '',
          bio: attributes.bio || '',
          skills: Array.isArray(attributes.skills) ? attributes.skills : [],
          isSignedIn: true,
          nextStep: { signInStep: "DONE" },
        };

        // Update the user in the context
        setUser(userData);
        setIsAuthenticated(true);
        
        // Return the sign-in result with proper typing
        return signInResult as SignInOutput;
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to complete new password";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

/**
 * Custom hook that provides access to the authentication context.
 * Must be used within an AuthProvider.
 *
 * @returns The authentication context value
 * @throws {Error} If used outside of an AuthProvider
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
