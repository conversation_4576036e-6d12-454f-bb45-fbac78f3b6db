import { BehaviorSubject } from 'rxjs';
import type { AuthUser } from '@/types/user';
import { API_CONFIG } from '@/config/api';

const isBrowser = typeof window !== 'undefined';

const getAuthClient = async () => {
  if (!isBrowser) return null;
  return await import('aws-amplify/auth');
};



export class AuthService {
  private static instance: AuthService;
  public userCredentials = new BehaviorSubject<AuthUser>({
    email: '',
    password: '',
    fromSignup: false,
  });

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  public updateUserCredentials(data: Partial<AuthUser>) {
    this.userCredentials.next({
      ...this.userCredentials.value,
      ...data,
    });
  }

  public async signUp(user: AuthUser, role: string = 'CLIENT') {
    if (!isBrowser) {
      throw new Error('Auth operations can only be performed in the browser');
    }

    try {
      const signupUrl = API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP);
      
      const response = await fetch(signupUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          password: user.password,
          name: user.name || user.email.split('@')[0],
          role,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        // Handle specific error codes from our Lambda function
        if (result.error === 'ACCOUNT_EXISTS') {
          throw new Error('An account with this email already exists. Please try logging in instead.');
        } else if (result.error === 'INVALID_PASSWORD') {
          throw new Error('Password does not meet requirements. Please ensure it is at least 8 characters long.');
        } else if (result.error === 'MISSING_FIELDS') {
          throw new Error('Please fill in all required fields.');
        } else if (result.error === 'INVALID_PARAMETERS') {
          throw new Error('Invalid information provided. Please check your details and try again.');
        }

        // Fallback to the message from the response or a generic error
        const errorMessage = result.message || 'Failed to sign up. Please try again.';
        throw new Error(errorMessage);
      }

      // Update user credentials with the new user data
      this.updateUserCredentials({
        email: user.email,
        name: user.name,
        fromSignup: true,
      });

      return {
        userId: result.userId || 'temp-id', // Lambda doesn't return userId yet, but we can add it later
        isSignUpComplete: true,
        nextStep: {
          signUpStep: 'CONFIRM_SIGN_UP',
        },
      };
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  }

  public async signIn(username: string, password: string) {
    if (!isBrowser) {
      throw new Error('Auth operations can only be performed in the browser');
    }

    try {
      const auth = await getAuthClient();
      if (!auth) throw new Error('Auth client not available');

      const signInResponse = await auth.signIn({ username, password });
      
      if (signInResponse.isSignedIn) {
        return await auth.getCurrentUser();
      }
      
      return signInResponse;
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  }

  public async setNewPassword(username: string, newPassword: string) {
    if (!isBrowser) {
      throw new Error('Auth operations can only be performed in the browser');
    }

    try {
      const auth = await getAuthClient();
      if (!auth) throw new Error('Auth client not available');

      const signInResponse = await auth.signIn({
        username,
        password: newPassword
      });
      
      if (signInResponse.isSignedIn) {
        return { success: true };
      }
      
      return signInResponse;
    } catch (error) {
      console.error('Error setting new password:', error);
      throw error;
    }
  }

  public async signOut() {
    if (!isBrowser) {
      return;
    }

    try {
      const auth = await getAuthClient();
      if (auth) {
        await auth.signOut();
      }
      this.userCredentials.next({
        email: '',
        password: '',
        fromSignup: false,
      });
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  public async getCurrentUser() {
    if (typeof window === 'undefined') {
      return null;
    }

    if (!isBrowser) {
      return null;
    }

    try {
      const { getCurrentUser: amplifyGetCurrentUser, fetchAuthSession } = await import('aws-amplify/auth');
      
      const user = await amplifyGetCurrentUser();
      
      if (user) {
        try {
          const session = await fetchAuthSession();
          return {
            ...user,
            signInDetails: user.signInDetails ? {
              loginId: user.signInDetails.loginId,
              authFlowType: user.signInDetails.authFlowType
            } : undefined,
            session
          };
        } catch {
          return {
            ...user,
            signInDetails: user.signInDetails ? {
              loginId: user.signInDetails.loginId,
              authFlowType: user.signInDetails.authFlowType
            } : undefined,
            session: null
          };
        }
      }
      
      return null;
    } catch (error) {
      const errorObj = error as Error & { name?: string; message?: string };
      if (errorObj?.name !== 'UserUnAuthenticatedException' &&
          !errorObj?.message?.includes?.('No credentials')) {
        console.error('Error getting current user:', error);
      }
      return null;
    }
  }

  public async getCurrentSession() {
    if (typeof window === 'undefined') {
      return null;
    }

    if (!isBrowser) {
      return null;
    }

    try {
      const { getCurrentUser, fetchAuthSession } = await import('aws-amplify/auth');
      
      try {
        await getCurrentUser();
        return await fetchAuthSession();
      } catch (userError) {
        if (userError && 
            typeof userError === 'object' && 
            'name' in userError && 
            userError.name === 'UserUnAuthenticatedException') {
          return null;
        }
        throw userError;
      }
    } catch (error) {
      const errorObj = error as Error & { name?: string; message?: string };
      if (errorObj?.name !== 'UserUnAuthenticatedException' &&
          !errorObj?.message?.includes?.('No credentials')) {
        console.error('Error getting current session:', error);
      }
      return null;
    }
  }

  public async confirmSignUp(username: string, code: string) {
    if (!isBrowser) {
      throw new Error('Auth operations can only be performed in the browser');
    }

    try {
      const auth = await getAuthClient();
      if (!auth) throw new Error('Auth client not available');

      const { isSignUpComplete, nextStep } = await auth.confirmSignUp({
        username,
        confirmationCode: code,
      });
      return { isSignUpComplete, nextStep };
    } catch (error) {
      console.error('Error confirming sign up:', error);
      throw error;
    }
  }

  public async forgotPassword(username: string) {
    if (!isBrowser) {
      throw new Error('Auth operations can only be performed in the browser');
    }

    try {
      const auth = await getAuthClient();
      if (!auth) throw new Error('Auth client not available');

      const response = await auth.resetPassword({ username });
      return response;
    } catch (error) {
      console.error('Error initiating forgot password:', error);
      throw error;
    }
  }

  public async forgotPasswordSubmit(
    username: string,
    code: string,
    newPassword: string
  ) {
    if (!isBrowser) {
      throw new Error('Auth operations can only be performed in the browser');
    }

    try {
      const auth = await getAuthClient();
      if (!auth) throw new Error('Auth client not available');

      await auth.confirmResetPassword({
        username,
        confirmationCode: code,
        newPassword,
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error confirming new password:', error);
      throw error;
    }
  }
}

export const authService = AuthService.getInstance();
