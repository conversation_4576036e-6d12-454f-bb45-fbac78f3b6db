const {
  CognitoIdentityProviderClient,
  AdminCreateUserCommand,
  AdminAddUserToGroupCommand,
} = require("@aws-sdk/client-cognito-identity-provider");

const client = new CognitoIdentityProviderClient({ region: process.env.REGION });

exports.handler = async (event) => {
  try {
    const body = JSON.parse(event.body);
    const { email, password, role } = body;

    if (!email || !password || !role) {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({
          message: "Missing required fields: email, password, role",
          error: "MISSING_FIELDS"
        }),
      };
    }

    const userPoolId = process.env.AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLID;
    const groupName = role || process.env.DEFAULT_USER_GROUP;

    // 1. Create the user with temporary password (triggers verification email)
    await client.send(
      new AdminCreateUserCommand({
        UserPoolId: userPoolId,
        Username: email,
        UserAttributes: [
          { Name: "email", Value: email },
          { Name: "email_verified", Value: "false" },
          { Name: "name", Value: body.name || email.split('@')[0] },
        ],
        TemporaryPassword: password + "Temp123!", // Temporary password
        MessageAction: "RESEND", // Send verification email
      })
    );

    // 2. Add user to the group
    await client.send(
      new AdminAddUserToGroupCommand({
        UserPoolId: userPoolId,
        Username: email,
        GroupName: groupName,
      })
    );

    return {
      statusCode: 201,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({
        message: "User created successfully. Please check your email for verification code.",
        email: email,
        requiresVerification: true
      }),
    };
  } catch (err) {
    console.error("Signup error:", err);

    // Handle specific Cognito errors
    if (err.name === 'UsernameExistsException') {
      return {
        statusCode: 409,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({
          message: "User account already exists",
          error: "ACCOUNT_EXISTS",
          email: JSON.parse(event.body).email
        }),
      };
    }

    if (err.name === 'InvalidPasswordException') {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({
          message: "Password does not meet requirements",
          error: "INVALID_PASSWORD"
        }),
      };
    }

    if (err.name === 'InvalidParameterException') {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({
          message: "Invalid parameters provided",
          error: "INVALID_PARAMETERS"
        }),
      };
    }

    // Generic error for other cases
    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({
        message: "Signup failed",
        error: "INTERNAL_ERROR",
        details: err.message
      }),
    };
  }
};
