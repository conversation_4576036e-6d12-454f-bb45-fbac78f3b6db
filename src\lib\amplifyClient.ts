import { Amplify } from 'aws-amplify';
import { fetchAuthSession } from 'aws-amplify/auth';
import { Hub } from 'aws-amplify/utils';
import { generateClient } from 'aws-amplify/api';
import awsconfig from '../aws-exports';

const isLocalhost = typeof window !== 'undefined' && 
  (window.location.hostname === 'localhost' || 
   window.location.hostname === '127.0.0.1' ||
   window.location.hostname.endsWith('localhost'));

const config = {
  ...awsconfig,
  Auth: {
    ...awsconfig.oauth,
    oauth: {
      ...awsconfig.oauth,
      redirectSignIn: isLocalhost ? 'http://localhost:3000/' : 'YOUR_PRODUCTION_URL',
      redirectSignOut: isLocalhost ? 'http://localhost:3000/login' : 'YOUR_PRODUCTION_URL/login',
      responseType: 'code',
    },
    cookieStorage: {
      domain: isLocalhost ? 'localhost' : 'yourdomain.com',
      path: '/',
      expires: 30,
      sameSite: 'lax',
      secure: !isLocalhost,
    },
  },
  ssr: true,
};

Amplify.configure(config);

const client = generateClient({
  authMode: 'userPool',
  authToken: (async () => {
    const session = await fetchAuthSession();
    return session.tokens?.idToken?.toString() || '';
  })() as unknown as string,
});


export function ensureAmplifyConfigured() {
  if (!Amplify.getConfig().Auth) {
    Amplify.configure(config);
  }
  return true;
}

export { client, Hub, fetchAuthSession };
